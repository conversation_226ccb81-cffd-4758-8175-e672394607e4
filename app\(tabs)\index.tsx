import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput, 
  Image,
  Dimensions, 
  FlatList
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  Search, 
  ShieldCheck, 
  Award, 
  FileText, 
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  Users,
  Zap,
  Filter,
  X,
  CheckCircle,
  Clock
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';

const { width } = Dimensions.get('window');
const certificationData = [
  {
    id: 1,
    name: 'AVS Halal',
    fullName: 'Association de Vérification et de Surveillance Halal',
    type: 'Organisme de certification',
    country: 'France',
    productsCount: 15420,
    logo: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg',
    verified: true,
    description: 'Organisme français reconnu pour la certification halal'
  },
  {
    id: 2,
    name: 'IFANCA',
    fullName: 'Islamic Food and Nutrition Council of America',
    type: 'Organisme de certification',
    country: 'États-Unis',
    productsCount: 8750,
    logo: 'https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg',
    verified: true,
    description: 'Organisme américain de certification halal reconnu mondialement'
  },
  {
    id: 3,
    name: 'Mosquée de Paris',
    fullName: 'Grande Mosquée de Paris - Certification Halal',
    type: 'Organisme de certification',
    country: 'France',
    productsCount: 12300,
    logo: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg',
    verified: true,
    description: 'Certification halal de la Grande Mosquée de Paris'
  },
  {
    id: 4,
    name: 'HFCE',
    fullName: 'Halal Food Council of Europe',
    type: 'Organisme de certification',
    country: 'Europe',
    productsCount: 6890,
    logo: 'https://images.pexels.com/photos/4110104/pexels-photo-4110104.jpeg',
    verified: true,
    description: 'Conseil européen pour la certification halal'
  },
  {
    id: 5,
    name: 'MUI Halal',
    fullName: 'Majelis Ulama Indonesia',
    type: 'Organisme de certification',
    country: 'Indonésie',
    productsCount: 25600,
    logo: 'https://images.pexels.com/photos/1435735/pexels-photo-1435735.jpeg',
    verified: true,
    description: 'Organisme indonésien de certification halal'
  },
  {
    id: 6,
    name: 'JAKIM',
    fullName: 'Jabatan Kemajuan Islam Malaysia',
    type: 'Organisme de certification',
    country: 'Malaisie',
    productsCount: 18900,
    logo: 'https://images.pexels.com/photos/1055272/pexels-photo-1055272.jpeg',
    verified: true,
    description: 'Département du développement islamique de Malaisie'
  }
];

export default function HomeScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchResults, setSearchResults] = useState([]);

  const navigation = useNavigation();

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    
    if (query.trim().length > 0) {
      const filtered = certificationData.filter(cert =>
        cert.name.toLowerCase().includes(query.toLowerCase()) ||
        cert.fullName.toLowerCase().includes(query.toLowerCase()) ||
        cert.country.toLowerCase().includes(query.toLowerCase())
      );
      setSearchResults(filtered);
      setShowSearchResults(true);
    } else {
      setShowSearchResults(false);
      setSearchResults([]);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    setShowSearchResults(false);
    setSearchResults([]);
  };

  const renderSearchResult = ({ item }) => (
    <TouchableOpacity style={styles.searchResultItem}>
      <Image source={{ uri: item.logo }} style={styles.searchResultLogo} />
      <View style={styles.searchResultInfo}>
        <View style={styles.searchResultHeader}>
          <Text style={styles.searchResultName}>{item.name}</Text>
          {item.verified && (
            <View style={styles.verifiedBadgeSmall}>
              <CheckCircle size={14} color="#10B981" strokeWidth={2} />
            </View>
          )}
        </View>
        <Text style={styles.searchResultFullName}>{item.fullName}</Text>
        <Text style={styles.searchResultDescription}>{item.description}</Text>
        <View style={styles.searchResultFooter}>
          <Text style={styles.searchResultCountry}>{item.country}</Text>
          <Text style={styles.searchResultProducts}>
            {item.productsCount.toLocaleString()} produits
          </Text>
        </View>
      </View>
    </TouchableOpacity>

    
  );
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Image source={require('../../assets/logo.jpg')} style={styles.logo} />
          </View>
          <View style={styles.headerStats}>
            <View style={styles.statItem}>
              <TrendingUp size={16} color="#10B981" />
              <Text style={styles.statText}>+2.5k</Text>
            </View>
            <View style={styles.statItem}>
              <Users size={16} color="#10B981" />
              <Text style={styles.statText}>Certifiés</Text>
            </View>
          </View>
        </View>

        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Text style={styles.heroTitle}>
            Certification Halal de Confiance
          </Text>
          <Text style={styles.heroSubtitle}>
            Votre partenaire de référence pour la certification et la traçabilité halal en France
          </Text>
        </View>

        {/* Search Section */}
        <View style={styles.searchSection}>
          <Text style={styles.searchTitle}>Rechercher un produit</Text>
          <Text style={styles.searchSubtitle}>
            Nous offrons la possibilité devérifier le caractère Halal de vos achats.
          </Text>
          
          <View style={styles.searchContainer}>
            <View style={styles.searchBar}>
              <Search size={20} color="#9CA3AF" strokeWidth={2} />
              <TextInput
                style={styles.searchInput}
                placeholder="Entrez un numero de lot"
                disableKeyboardShortcuts
                value={searchQuery}
                onPress={() => navigation.navigate('certifications')}
                placeholderTextColor="#9CA3AF"
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
                  <X size={18} color="#9CA3AF" strokeWidth={2} />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Search Results */}
          {showSearchResults && (
            <View style={styles.searchResults}>
              <View style={styles.searchResultsHeader}>
                <Text style={styles.searchResultsTitle}>
                  {searchResults.length} résultat{searchResults.length > 1 ? 's' : ''} trouvé{searchResults.length > 1 ? 's' : ''}
                </Text>
              </View>
              <FlatList
                data={searchResults}
                renderItem={renderSearchResult}
                keyExtractor={(item) => item.id.toString()}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
              />
            </View>
          )}

          {/* Popular Certifications 
          {!showSearchResults && (
            <View style={styles.popularCertifications}>
              <Text style={styles.sectionTitle}>Certifications Populaires</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {certificationData.slice(0, 4).map((cert) => (
                  <TouchableOpacity key={cert.id} style={styles.popularCertCard}>
                    <Image source={{ uri: cert.logo }} style={styles.popularCertLogo} />
                    <Text style={styles.popularCertName}>{cert.name}</Text>
                    <Text style={styles.popularCertProducts}>
                      {(cert.productsCount / 1000).toFixed(1)}k produits
                    </Text>
                  </TouchableOpacity>
                ))}
                <TouchableOpacity>
                  <Text style={styles.seeAllText} onPress={() => navigation.navigate('certifications')}>Voir tout</Text>
                </TouchableOpacity>
              </ScrollView>
            </View>
          )}*/}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Services Complémentaires</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.actionCard}>
              <View style={[styles.actionIcon, { backgroundColor: '#DCFCE7' }]}>
                <Award size={24} color="#10B981" />
              </View>
              <Text style={styles.actionTitle}>Renouvellement</Text>
              <Text style={styles.actionSubtitle}>Renouvellement de vos certificats existants</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionCard}>
              <View style={[styles.actionIcon, { backgroundColor: '#FEF3C7' }]}>
                <FileText size={24} color="#F59E0B" />
              </View>
              <Text style={styles.actionTitle}>Documentation</Text>
              <Text style={styles.actionSubtitle}>Préparation de la documentation requise</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionCard}>
              <View style={[styles.actionIcon, { backgroundColor: '#E0E7FF' }]}>
                <Users size={24} color="#3B82F6" />
              </View>
              <Text style={styles.actionTitle}>Support Client</Text>
              <Text style={styles.actionSubtitle}>Accompagnement personnalisé 24/7</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionCard}>
              <View style={[styles.actionIcon, { backgroundColor: '#FCE7F3' }]}>
                <Clock size={24} color="#EC4899" />
              </View>
              <Text style={styles.actionTitle}>Express</Text>
              <Text style={styles.actionSubtitle}>Certification accélérée sous 48h</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Stats Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Nos Chiffres</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>2,500+</Text>
              <Text style={styles.statLabel}>Produits Certifiés</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>300+</Text>
              <Text style={styles.statLabel}>Entreprises</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>15</Text>
              <Text style={styles.statLabel}>Années d'Expérience</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>99%</Text>
              <Text style={styles.statLabel}>Satisfaction Client</Text>
            </View>
          </View>
        </View>

        {/* Services Preview */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Nos Services</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText} onPress={() => navigation.navigate('services')}>Voir tout</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.servicesScroll}>
            <View style={styles.serviceCard}>
              <Image
                source={{ uri: 'https://images.pexels.com/photos/4110104/pexels-photo-4110104.jpeg' }}
                style={styles.serviceImage}
              />
              <View style={styles.serviceContent}>
                <Text style={styles.serviceTitle}>Certification Halal</Text>
                <Text style={styles.serviceDescription}>
                  Certification complète de vos produits selon les standards islamiques les plus stricts
                </Text>
                <View style={styles.serviceRating}>
                  <Star size={14} color="#F59E0B" fill="#F59E0B" />
                  <Text style={styles.ratingText}>4.9</Text>
                </View>
              </View>
            </View>

            <View style={styles.serviceCard}>
              <Image
                source={{ uri: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg' }}
                style={styles.serviceImage}
              />
              <View style={styles.serviceContent}>
                <Text style={styles.serviceTitle}>Audit de Conformité</Text>
                <Text style={styles.serviceDescription}>
                 Vérification approfondie de la conformité halal de votre chaîne de production
                </Text>
                <View style={styles.serviceRating}>
                  <Star size={14} color="#F59E0B" fill="#F59E0B" />
                  <Text style={styles.ratingText}>4.8</Text>
                </View>
              </View>
            </View>

            <View style={styles.serviceCard}>
              <Image
                source={{ uri: 'https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg' }}
                style={styles.serviceImage}
              />
              <View style={styles.serviceContent}>
                <Text style={styles.serviceTitle}>Formation Halal</Text>
                <Text style={styles.serviceDescription}>
                  Formation complète de vos équipes aux exigences et bonnes pratiques halal
                </Text>
                <View style={styles.serviceRating}>
                  <Star size={14} color="#F59E0B" fill="#F59E0B" />
                  <Text style={styles.ratingText}>5.0</Text>
                </View>
              </View>
            </View>

            <View style={styles.serviceCard}>
              <Image
                source={{ uri: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg' }}
                style={styles.serviceImage}
              />
              <View style={styles.serviceContent}>
                <Text style={styles.serviceTitle}>Conseil Stratégique</Text>
                <Text style={styles.serviceDescription}>
                  Accompagnement personnalisé pour développer votre stratégie halal
                </Text>
                <View style={styles.serviceRating}>
                  <Star size={14} color="#F59E0B" fill="#F59E0B" />
                  <Text style={styles.ratingText}>5.0</Text>
                </View>
              </View>
            </View>
          </ScrollView>
        </View>

        {/* Contact Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Nous Contacter</Text>
          <View style={styles.contactGrid}>
            <TouchableOpacity style={styles.contactCard}>
              <Phone size={24} color="#10B981" />
              <Text style={styles.contactTitle}>Téléphone</Text>
              <Text style={styles.contactText}>+33 (0)148304863</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.contactCard}>
              <Mail size={24} color="#3B82F6" />
              <Text style={styles.contactTitle}>Email</Text>
              <Text style={styles.contactText}><EMAIL></Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.contactCard}>
              <MapPin size={24} color="#EF4444" />
              <Text style={styles.contactTitle}>Adresse</Text>
              <Text style={styles.contactText}>Paris, France</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* CTA Section */}
        <View style={styles.ctaSection}>
          <LinearGradient
            colors={['#059669', '#10B981']}
            style={styles.ctaCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}>
            <Zap size={48} color="#FFFFFF" strokeWidth={2} />
            <Text style={styles.ctaTitle}>Prêt à commencer ?</Text>
            <Text style={styles.ctaSubtitle}>
              Contactez nos experts pour discuter de votre projet et obtenir un devis personnalisé
            </Text>
            
            <View style={styles.ctaButtons}>
              <TouchableOpacity style={styles.ctaPrimaryButton}>
                <Text style={styles.ctaPrimaryButtonText} onPress={() => navigation.navigate('profile')}>Demander un devis</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.ctaSecondaryButton}>
                <Phone size={20} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.ctaSecondaryButtonText}>Nous appeler</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.contactInfo}>
              <View style={styles.contactItem}>
                <Mail size={16} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.contactText}><EMAIL></Text>
              </View>
              <View style={styles.contactItem}>
                <Phone size={16} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.contactText}>+33 (0)148304863</Text>
              </View>
            </View>
          </LinearGradient>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerContent: {
    flex: 1,
  },
  logo: {
    width: 70,
    height: 50,
    borderRadius: 2,
    marginRight: 12,
  },
  logoText: {
    fontFamily: 'Inter-Bold',
    fontSize: 24,
    color: '#10B981',
  },
  logoSubtext: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    color: '#6B7280',
  },
  headerStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontFamily: 'Inter-Medium',
    fontSize: 12,
    color: '#374151',
  },
  heroSection: {
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  heroTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
    color: '#111827',
    marginBottom: 12,
    lineHeight: 36,
  },
  heroSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 24,
    marginBottom: 24,
  },
  
  searchIcon: {
    marginRight: 12,
  },
  
  section: {
    padding: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 20,
    color: '#111827',
  },
  seeAllText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    color: '#10B981',
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    width: (width - 64) / 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  actionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    color: '#111827',
    marginBottom: 4,
  },
  actionSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    color: '#6B7280',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    width: (width - 64) / 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statNumber: {
    fontFamily: 'Inter-Bold',
    fontSize: 24,
    color: '#10B981',
    marginBottom: 4,
  },
  statLabel: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  servicesScroll: {
    marginTop: 16,
  },
  serviceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginRight: 16,
    marginBottom: 16,
    width: 240,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  serviceImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  serviceContent: {
    padding: 16,
  },
  serviceTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    color: '#111827',
    marginBottom: 8,
  },
  serviceDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 12,
  },
  serviceRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    color: '#111827',
  },
  contactGrid: {
    gap: 12,
  },
  contactCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  contactTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    color: '#111827',
    flex: 1,
  },
  ctaSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  ctaCard: {
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.3,
    shadowRadius: 24,
    elevation: 12,
  },
  ctaTitle: {
    fontSize: 24,
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  ctaSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    opacity: 0.9,
    marginBottom: 32,
    lineHeight: 24,
  },
  ctaButtons: {
    width: '100%',
    gap: 16,
    marginBottom: 32,
  },
  ctaPrimaryButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    alignItems: 'center',
  },
  ctaPrimaryButtonText: {
    color: '#059669',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  ctaSecondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  ctaSecondaryButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginLeft: 8,
  },
  contactInfo: {
    alignItems: 'center',
    gap: 12,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactText: {
    color: '#000000',
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginLeft: 8,
    opacity: 0.9,
  },
  relatedServices: {
    flexDirection: 'row',
    paddingLeft: 24,
    gap: 16,
  },


  searchSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  searchTitle: {
    fontSize: 20,
    color: '#0F172A',
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  searchSubtitle: {
    fontSize: 12,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    marginBottom: 20,
    lineHeight: 24,
  },
  searchContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#0F172A',
    fontFamily: 'Inter-Regular',
  },
  clearButton: {
    padding: 4,
  },
  filterButton: {
    width: 48,
    height: 48,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  searchResults: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  searchResultsHeader: {
    marginBottom: 16,
  },
  searchResultsTitle: {
    fontSize: 16,
    color: '#0F172A',
    fontFamily: 'Inter-SemiBold',
    letterSpacing: -0.3,
  },
  searchResultItem: {
    flexDirection: 'row',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  searchResultLogo: {
    width: 56,
    height: 56,
    borderRadius: 28,
    marginRight: 16,
  },
  searchResultInfo: {
    flex: 1,
  },
  searchResultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  searchResultName: {
    fontSize: 16,
    color: '#0F172A',
    fontFamily: 'Inter-SemiBold',
    letterSpacing: -0.3,
    flex: 1,
  },
  verifiedBadgeSmall: {
    marginLeft: 8,
  },
  searchResultFullName: {
    fontSize: 14,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    marginBottom: 4,
  },
  searchResultDescription: {
    fontSize: 13,
    color: '#9CA3AF',
    fontFamily: 'Inter-Regular',
    marginBottom: 8,
    lineHeight: 18,
  },
  searchResultFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  searchResultCountry: {
    fontSize: 12,
    color: '#667eea',
    fontFamily: 'Inter-Medium',
  },
  searchResultProducts: {
    fontSize: 12,
    color: '#9CA3AF',
    fontFamily: 'Inter-Regular',
  },
  popularCertifications: {
    marginTop: 8,
  },
  popularTitle: {
    fontSize: 20,
    color: '#0F172A',
    fontFamily: 'Inter-SemiBold',
    letterSpacing: -0.3,
  },
  popularCertCard: {
    width: 120,
    backgroundColor: '#FFFFFF',
    padding: 16,
    marginTop: 2,
    marginBottom:16,
    borderRadius: 16,
    marginRight: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  popularCertLogo: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginBottom: 12,
  },
  popularCertName: {
    fontSize: 14,
    color: '#0F172A',
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
    marginBottom: 4,
    letterSpacing: -0.3,
  },
  popularCertProducts: {
    fontSize: 11,
    color: '#9CA3AF',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
});