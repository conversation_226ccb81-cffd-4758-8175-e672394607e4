import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams, router } from 'expo-router';
import { ArrowLeft, ShieldCheck, CircleCheck as CheckCircle, BookOpen, Target, Clock, Users, Award, Phone, Mail, Star, Calendar, FileText, Zap, Globe } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';

const { width } = Dimensions.get('window');

const serviceDetails = {
  '1': {
    title: 'Certification Halal',
    description: 'Certification complète de vos produits selon les standards islamiques les plus stricts',
    icon: ShieldCheck,
    color: '#059669',
    gradient: ['#059669', '#10B981'],
    image: 'https://images.pexels.com/photos/4110104/pexels-photo-4110104.jpeg',
    overview: 'Notre service de certification halal garantit que vos produits respectent scrupuleusement les exigences islamiques. Nous effectuons un audit complet de votre chaîne de production, depuis les matières premières jusqu\'au produit fini.',
    features: [
      'Audit complet des ingrédients et additifs',
      'Inspection détaillée des processus de production',
      'Vérification des équipements et ustensiles',
      'Formation du personnel aux bonnes pratiques',
      'Certification officielle reconnue internationalement',
      'Suivi continu et renouvellement périodique'
    ],
    process: [
      {
        step: 1,
        title: 'Demande initiale',
        description: 'Soumission du dossier et analyse préliminaire de votre demande'
      },
      {
        step: 2,
        title: 'Audit documentaire',
        description: 'Examen approfondi de tous vos documents et procédures'
      },
      {
        step: 3,
        title: 'Inspection sur site',
        description: 'Visite de vos installations par nos experts certifiés'
      },
      {
        step: 4,
        title: 'Rapport d\'évaluation',
        description: 'Remise du rapport détaillé avec recommandations'
      },
      {
        step: 5,
        title: 'Certification',
        description: 'Délivrance du certificat halal officiel'
      }
    ],
    benefits: [
      'Accès aux marchés musulmans mondiaux',
      'Augmentation de la confiance des consommateurs',
      'Différenciation concurrentielle',
      'Conformité aux réglementations internationales',
      'Amélioration de l\'image de marque'
    ],
    requirements: [
      'Dossier technique complet du produit',
      'Liste détaillée des ingrédients et additifs',
      'Procédures de fabrication documentées',
      'Certificats des fournisseurs',
      'Plans des installations'
    ]
  },
  '2': {
    title: 'Audit de Conformité',
    description: 'Vérification approfondie de la conformité halal de votre chaîne de production',
    icon: CheckCircle,
    color: '#3B82F6',
    gradient: ['#3B82F6', '#60A5FA'],
    image: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg',
    overview: 'Notre audit de conformité halal évalue l\'ensemble de votre chaîne de production pour identifier les points critiques et garantir le respect des standards halal les plus exigeants.',
    features: [
      'Analyse complète des fournisseurs',
      'Contrôle rigoureux des équipements',
      'Évaluation des procédures de nettoyage',
      'Formation spécialisée du personnel',
      'Rapport détaillé avec plan d\'action',
      'Accompagnement dans la mise en conformité'
    ],
    process: [
      {
        step: 1,
        title: 'Planification',
        description: 'Définition du périmètre d\'audit et planification des interventions'
      },
      {
        step: 2,
        title: 'Audit terrain',
        description: 'Inspection complète de vos installations et processus'
      },
      {
        step: 3,
        title: 'Analyse',
        description: 'Évaluation des non-conformités et identification des risques'
      },
      {
        step: 4,
        title: 'Rapport',
        description: 'Remise du rapport d\'audit avec recommandations prioritaires'
      },
      {
        step: 5,
        title: 'Suivi',
        description: 'Accompagnement dans la mise en œuvre des corrections'
      }
    ],
    benefits: [
      'Identification proactive des risques',
      'Amélioration continue des processus',
      'Réduction des coûts de non-conformité',
      'Préparation optimale à la certification',
      'Renforcement de la crédibilité'
    ],
    requirements: [
      'Accès complet aux installations',
      'Documentation des processus existants',
      'Disponibilité des équipes opérationnelles',
      'Historique des contrôles qualité',
      'Engagement de la direction'
    ]
  },
  '3': {
    title: 'Formation Halal',
    description: 'Formation complète de vos équipes aux exigences et bonnes pratiques halal',
    icon: BookOpen,
    color: '#8B5CF6',
    gradient: ['#8B5CF6', '#A78BFA'],
    image: 'https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg',
    overview: 'Nos formations halal permettent à vos équipes d\'acquérir toutes les compétences nécessaires pour maintenir la conformité halal au quotidien et garantir la qualité de vos produits.',
    features: [
      'Modules théoriques et pratiques adaptés',
      'Certification individuelle du personnel',
      'Support pédagogique complet fourni',
      'Évaluations et tests de compétences',
      'Suivi post-formation personnalisé',
      'Mise à jour continue des connaissances'
    ],
    process: [
      {
        step: 1,
        title: 'Évaluation des besoins',
        description: 'Analyse des compétences existantes et définition des objectifs'
      },
      {
        step: 2,
        title: 'Programme personnalisé',
        description: 'Conception d\'un programme adapté à votre secteur d\'activité'
      },
      {
        step: 3,
        title: 'Formation théorique',
        description: 'Enseignement des principes fondamentaux du halal'
      },
      {
        step: 4,
        title: 'Ateliers pratiques',
        description: 'Mise en situation et exercices concrets'
      },
      {
        step: 5,
        title: 'Certification',
        description: 'Évaluation finale et délivrance des certificats'
      }
    ],
    benefits: [
      'Personnel qualifié et certifié',
      'Réduction des erreurs de production',
      'Amélioration de la qualité',
      'Motivation et engagement des équipes',
      'Conformité réglementaire assurée'
    ],
    requirements: [
      'Identification des participants',
      'Salle de formation équipée',
      'Disponibilité sur la durée complète',
      'Engagement de participation active',
      'Support de la hiérarchie'
    ]
  },
  '4': {
    title: 'Conseil Stratégique',
    description: 'Accompagnement personnalisé pour développer votre stratégie halal',
    icon: Target,
    color: '#F59E0B',
    gradient: ['#F59E0B', '#FBBF24'],
    image: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg',

    duration: 'Variable',
    overview: 'Notre service de conseil stratégique vous accompagne dans le développement d\'une stratégie halal cohérente et rentable, adaptée à vos objectifs business et à votre marché cible.',
    features: [
      'Analyse approfondie de votre marché',
      'Stratégie de positionnement optimale',
      'Plan de développement personnalisé',
      'Étude de faisabilité détaillée',
      'Accompagnement continu et suivi',
      'Optimisation du retour sur investissement'
    ],
    process: [
      {
        step: 1,
        title: 'Diagnostic initial',
        description: 'Analyse de votre situation actuelle et de vos objectifs'
      },
      {
        step: 2,
        title: 'Étude de marché',
        description: 'Analyse concurrentielle et identification des opportunités'
      },
      {
        step: 3,
        title: 'Stratégie',
        description: 'Élaboration de la stratégie halal optimale'
      },
      {
        step: 4,
        title: 'Plan d\'action',
        description: 'Définition des étapes et du calendrier de mise en œuvre'
      },
      {
        step: 5,
        title: 'Accompagnement',
        description: 'Suivi de la mise en œuvre et ajustements'
      }
    ],
    benefits: [
      'Stratégie alignée sur vos objectifs',
      'Optimisation des investissements',
      'Accélération du time-to-market',
      'Minimisation des risques',
      'Expertise sectorielle reconnue'
    ],
    requirements: [
      'Définition claire des objectifs',
      'Accès aux données de l\'entreprise',
      'Implication de la direction',
      'Budget dédié au projet',
      'Engagement sur la durée'
    ]
  }
};

export default function ServiceDetailScreen() {
  const { id } = useLocalSearchParams();
  const service = serviceDetails[id as string];
  const navigation = useNavigation();

  if (!service) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Service non trouvé</Text>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Text style={styles.backButtonText}>Retour</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ArrowLeft size={24} color="#FFFFFF" strokeWidth={2} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Détails du service</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Image source={{ uri: service.image }} style={styles.heroImage} />
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)']}
            style={styles.heroOverlay}
          />
          <View style={styles.heroContent}>
            <LinearGradient
              colors={service.gradient}
              style={styles.heroIcon}>
              <service.icon size={32} color="#FFFFFF" strokeWidth={2} />
            </LinearGradient>
            <Text style={styles.heroTitle}>{service.title}</Text>
            <Text style={styles.heroDescription}>{service.description}</Text>
            
          </View>
        </View>

        {/* Overview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Présentation</Text>
          <Text style={styles.overviewText}>{service.overview}</Text>
        </View>

        {/* Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ce qui est inclus</Text>
          <View style={styles.featuresList}>
            {service.features.map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <CheckCircle size={20} color="#10B981" strokeWidth={2} />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Process */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notre processus</Text>
          <View style={styles.processList}>
            {service.process.map((step, index) => (
              <View key={index} style={styles.processItem}>
                <View style={styles.processStep}>
                  <Text style={styles.processStepNumber}>{step.step}</Text>
                </View>
                <View style={styles.processContent}>
                  <Text style={styles.processTitle}>{step.title}</Text>
                  <Text style={styles.processDescription}>{step.description}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Benefits */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Bénéfices pour votre entreprise</Text>
          <View style={styles.benefitsList}>
            {service.benefits.map((benefit, index) => (
              <View key={index} style={styles.benefitItem}>
                <Star size={18} color="#F59E0B" fill="#F59E0B" strokeWidth={1} />
                <Text style={styles.benefitText}>{benefit}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Requirements */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Prérequis</Text>
          <View style={styles.requirementsList}>
            {service.requirements.map((requirement, index) => (
              <View key={index} style={styles.requirementItem}>
                <FileText size={18} color="#6B7280" strokeWidth={2} />
                <Text style={styles.requirementText}>{requirement}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* CTA Section */}
        <View style={styles.ctaSection}>
          <LinearGradient
            colors={service.gradient}
            style={styles.ctaCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}>
            <Zap size={48} color="#FFFFFF" strokeWidth={2} />
            <Text style={styles.ctaTitle}>Prêt à commencer ?</Text>
            <Text style={styles.ctaSubtitle}>
              Contactez nos experts pour discuter de votre projet et obtenir un devis personnalisé
            </Text>
            
            <View style={styles.ctaButtons}>
              <TouchableOpacity style={styles.ctaPrimaryButton}>
                <Text style={styles.ctaPrimaryButtonText} onPress={() => navigation.navigate('profile')}>Demander un devis</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.ctaSecondaryButton}>
                <Phone size={20} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.ctaSecondaryButtonText}>Nous appeler</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.contactInfo}>
              <View style={styles.contactItem}>
                <Mail size={16} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.contactText}><EMAIL></Text>
              </View>
              <View style={styles.contactItem}>
                <Phone size={16} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.contactText}>+33 (0)148304863</Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Related Services */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Services complémentaires</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.relatedServices}>
              <TouchableOpacity style={styles.relatedServiceCard}>
                <Globe size={24} color="#059669" strokeWidth={2} />
                <Text style={styles.relatedServiceTitle}>Étiquetage Halal</Text>
                <Text style={styles.relatedServiceDescription}>
                  Conception et validation de vos étiquettes
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.relatedServiceCard}>
                <Users size={24} color="#3B82F6" strokeWidth={2} />
                <Text style={styles.relatedServiceTitle}>Support Technique</Text>
                <Text style={styles.relatedServiceDescription}>
                  Assistance 24/7 pour vos questions
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.relatedServiceCard}>
                <Calendar size={24} color="#8B5CF6" strokeWidth={2} />
                <Text style={styles.relatedServiceTitle}>Veille Réglementaire</Text>
                <Text style={styles.relatedServiceDescription}>
                  Informations sur les évolutions
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: '#F0FDF4',
    paddingTop: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#10B981',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    color: '#10B981',
    fontFamily: 'Inter-SemiBold',
  },
  placeholder: {
    width: 40,
  },
  heroSection: {
    height: 300,
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  heroOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  heroContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 24,
    alignItems: 'center',
  },
  heroIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  heroTitle: {
    fontSize: 28,
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  heroDescription: {
    fontSize: 16,
    color: '#FFFFFF',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    opacity: 0.9,
    marginBottom: 20,
    lineHeight: 24,
  },
  heroMeta: {
    flexDirection: 'row',
    gap: 24,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  metaText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    marginLeft: 8,
  },
  section: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 22,
    color: '#111827',
    fontFamily: 'Inter-Bold',
    marginBottom: 16,
    letterSpacing: -0.5,
  },
  overviewText: {
    fontSize: 16,
    color: '#374151',
    fontFamily: 'Inter-Regular',
    lineHeight: 24,
  },
  featuresList: {
    gap: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  featureText: {
    fontSize: 16,
    color: '#374151',
    fontFamily: 'Inter-Regular',
    marginLeft: 16,
    flex: 1,
    lineHeight: 24,
  },
  processList: {
    gap: 24,
  },
  processItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  processStep: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#059669',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  processStepNumber: {
    fontSize: 16,
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
  },
  processContent: {
    flex: 1,
  },
  processTitle: {
    fontSize: 18,
    color: '#111827',
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
    letterSpacing: -0.3,
  },
  processDescription: {
    fontSize: 15,
    color: '#6B7280',
    fontFamily: 'Inter-Regular',
    lineHeight: 22,
  },
  benefitsList: {
    gap: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  benefitText: {
    fontSize: 16,
    color: '#374151',
    fontFamily: 'Inter-Regular',
    marginLeft: 16,
    flex: 1,
    lineHeight: 24,
  },
  requirementsList: {
    gap: 16,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  requirementText: {
    fontSize: 16,
    color: '#374151',
    fontFamily: 'Inter-Regular',
    marginLeft: 16,
    flex: 1,
    lineHeight: 24,
  },
  ctaSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  ctaCard: {
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.3,
    shadowRadius: 24,
    elevation: 12,
  },
  ctaTitle: {
    fontSize: 24,
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  ctaSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    opacity: 0.9,
    marginBottom: 32,
    lineHeight: 24,
  },
  ctaButtons: {
    width: '100%',
    gap: 16,
    marginBottom: 32,
  },
  ctaPrimaryButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    alignItems: 'center',
  },
  ctaPrimaryButtonText: {
    color: '#059669',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  ctaSecondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  ctaSecondaryButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginLeft: 8,
  },
  contactInfo: {
    alignItems: 'center',
    gap: 12,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginLeft: 8,
    opacity: 0.9,
  },
  relatedServices: {
    flexDirection: 'row',
    paddingLeft: 24,
    gap: 16,
  },
  relatedServiceCard: {
    width: 160,
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  relatedServiceTitle: {
    fontSize: 14,
    color: '#111827',
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
    marginTop: 12,
    marginBottom: 8,
  },
  relatedServiceDescription: {
    fontSize: 12,
    color: '#6B7280',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 18,
    color: '#6B7280',
    fontFamily: 'Inter-Regular',
    marginBottom: 24,
  },
  backButtonText: {
    fontSize: 16,
    color: '#059669',
    fontFamily: 'Inter-SemiBold',
  },
  bottomSpacing: {
    height: 32,
  },
});