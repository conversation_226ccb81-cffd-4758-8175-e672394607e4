import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  Dimensions 
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ShieldCheck, Award, BookOpen, Search, FileText, Users, Clock, CircleCheck as CheckCircle, ArrowRight, Target, Phone, Mail, Zap } from 'lucide-react-native';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';

const { width } = Dimensions.get('window');

const mainServices = [
  {
    id: 1,
    title: 'Certification Halal',
    description: 'Certification complète de vos produits selon les standards islamiques les plus stricts',
    icon: ShieldCheck,
    color: '#059669',
    gradient: ['#059669', '#10B981'],
    features: [
      'Audit complet des ingrédients',
      'Inspection des processus de production',
      'Certification officielle reconnue',
      'Suivi et renouvellement'
    ],
    image: 'https://images.pexels.com/photos/4110104/pexels-photo-4110104.jpeg',
  },
  {
    id: 2,
    title: 'Audit de Conformité',
    description: 'Vérification approfondie de la conformité halal de votre chaîne de production',
    icon: CheckCircle,
    color: '#3B82F6',
    gradient: ['#3B82F6', '#60A5FA'],
    features: [
      'Analyse des fournisseurs',
      'Contrôle des équipements',
      'Formation du personnel',
      'Rapport détaillé'
    ],
    image: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg',
  },
  {
    id: 3,
    title: 'Formation Halal',
    description: 'Formation complète de vos équipes aux exigences et bonnes pratiques halal',
    icon: BookOpen,
    color: '#8B5CF6',
    gradient: ['#8B5CF6', '#A78BFA'],
    features: [
      'Modules théoriques et pratiques',
      'Certification du personnel',
      'Support pédagogique',
      'Suivi post-formation'
    ],
    image: 'https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg',
  },
  {
    id: 4,
    title: 'Conseil Stratégique',
    description: 'Accompagnement personnalisé pour développer votre stratégie halal',
    icon: Target,
    color: '#F59E0B',
    gradient: ['#F59E0B', '#FBBF24'],
    features: [
      'Analyse de marché',
      'Stratégie de positionnement',
      'Plan de développement',
      'Accompagnement continu'
    ],
    image: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg',
  }
];
const certificationBodies = [
  {
    name: 'AVS Halal',
    logo: 'https://scontent.ftun8-1.fna.fbcdn.net/v/t39.30808-6/376578374_10226405722880841_1054994842593481785_n.jpg?_nc_cat=106&ccb=1-7&_nc_sid=6ee11a&_nc_ohc=eNcll1z1h8oQ7kNvwFMEXFF&_nc_oc=AdlsOtQtx6bjvdq4yq8ngvYd791MkZrfqlVMYKG0ehyGS29ADBoyy3UZ-_Vmm1LaJMtUuf3nNpsGbXarax3Gvudz&_nc_zt=23&_nc_ht=scontent.ftun8-1.fna&_nc_gid=5o9XfU-Cq9Ws-4gJwBcvrQ&oh=00_AfRrMBm8Wc461iIJ9I6FSCyEL1Dn7ykSYKNve6So-t5uoA&oe=68702439',
    description: 'Association de Vérification et de Surveillance Halal'
  },
  
];
export default function ServicesScreen() {
  const handleServicePress = (serviceId: number) => {
    router.push(`/services/${serviceId}`);
  };
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Nos Services</Text>
          <Text style={styles.headerSubtitle}>
            Solutions complètes de certification halal
          </Text>
        </View>

        {/* Main Services */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Services Principaux</Text>
          {mainServices.map((service) => (
            <TouchableOpacity 
              style={styles.mainServiceCard}
              key={service.id}
              onPress={() => handleServicePress(service.id)}
            >
            <Image
              source={{ uri: service.image }}
              style={styles.serviceMainImage}
            />
            <View style={styles.serviceMainContent}>
              <View style={styles.serviceMainHeader}>
                <ShieldCheck size={24} color="#10B981" />
                <Text style={styles.serviceMainTitle}>{service.title}</Text>
              </View>
              <Text style={styles.serviceMainDescription}>
                {service.description}
              </Text>
               <View style={styles.serviceFeatures}>
                  {service.features.map((feature, index) => (
                    <View key={index} style={styles.feature}>
                      <CheckCircle size={14} color="#10B981" strokeWidth={2} />
                      <Text style={styles.featureText}>{feature}</Text>
                    </View>
                  ))}
                </View>
              <TouchableOpacity style={styles.serviceButton}>
                <Text style={styles.serviceButtonText}>En savoir plus</Text>
                <ArrowRight size={16} color="#10B981" />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
          ))}

        </View>

        {/* Certification Bodies */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Organismes de Certification</Text>
          <Text style={styles.sectionSubtitle}>
            Nous travaillons avec les organismes de certification les plus reconnus
          </Text>
          
          <View style={styles.certificationBodies}>
            {certificationBodies.map((body, index) => (
              <View key={index} style={styles.certificationCard}>
                <Image source={{ uri: body.logo }} style={styles.certificationLogo} />
                
              </View>
            ))}
          </View>
        </View>

        {/* Process Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Processus de Certification</Text>
          <Text style={styles.sectionSubtitle}>
            Un processus simple et transparent en 4 étapes
          </Text>
          
          <View style={styles.processContainer}>
            <View style={styles.processStep}>
              <View style={[styles.stepNumber, { backgroundColor: '#DCFCE7' }]}>
                <Text style={[styles.stepNumberText, { color: '#10B981' }]}>1</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Demande Initial</Text>
                <Text style={styles.stepDescription}>
                  Soumission de votre demande avec les informations de base
                </Text>
              </View>
            </View>

            <View style={styles.processStep}>
              <View style={[styles.stepNumber, { backgroundColor: '#FEF3C7' }]}>
                <Text style={[styles.stepNumberText, { color: '#F59E0B' }]}>2</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Audit sur Site</Text>
                <Text style={styles.stepDescription}>
                  Inspection complète de vos installations et processus
                </Text>
              </View>
            </View>

            <View style={styles.processStep}>
              <View style={[styles.stepNumber, { backgroundColor: '#E0E7FF' }]}>
                <Text style={[styles.stepNumberText, { color: '#3B82F6' }]}>3</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Évaluation</Text>
                <Text style={styles.stepDescription}>
                  Analyse des résultats et préparation du rapport
                </Text>
              </View>
            </View>

            <View style={styles.processStep}>
              <View style={[styles.stepNumber, { backgroundColor: '#FCE7F3' }]}>
                <Text style={[styles.stepNumberText, { color: '#EC4899' }]}>4</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Certification</Text>
                <Text style={styles.stepDescription}>
                  Délivrance du certificat halal officiel
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Additional Services */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Services Complémentaires</Text>
          
          <View style={styles.additionalServices}>
            <TouchableOpacity style={styles.additionalServiceCard}>
              <Award size={32} color="#10B981" />
              <Text style={styles.additionalServiceTitle}>Renouvellement</Text>
              <Text style={styles.additionalServiceDescription}>
                Renouvellement de vos certificats existants
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.additionalServiceCard}>
              <FileText size={32} color="#3B82F6" />
              <Text style={styles.additionalServiceTitle}>Documentation</Text>
              <Text style={styles.additionalServiceDescription}>
                Préparation de la documentation requise
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.additionalServiceCard}>
              <Users size={32} color="#F59E0B" />
              <Text style={styles.additionalServiceTitle}>Support Client</Text>
              <Text style={styles.additionalServiceDescription}>
                Accompagnement personnalisé 24/7
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.additionalServiceCard}>
              <Clock size={32} color="#EF4444" />
              <Text style={styles.additionalServiceTitle}>Express</Text>
              <Text style={styles.additionalServiceDescription}>
                Certification accélérée sous 48h
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* CTA Section */}
        <View style={styles.ctaSection}>
          <LinearGradient
            colors={['#059669', '#10B981']}
            style={styles.ctaCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}>
            <Zap size={48} color="#FFFFFF" strokeWidth={2} />
            <Text style={styles.ctaTitle}>Prêt à commencer ?</Text>
            <Text style={styles.ctaSubtitle}>
              Contactez nos experts pour discuter de votre projet et obtenir un devis personnalisé
            </Text>
            
            <View style={styles.ctaButtons}>
              <TouchableOpacity style={styles.ctaPrimaryButton}>
                <Text style={styles.ctaPrimaryButtonText} onPress={() => navigation.navigate('profile')}>Demander un devis</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.ctaSecondaryButton}>
                <Phone size={20} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.ctaSecondaryButtonText}>Nous appeler</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.contactInfo}>
              <View style={styles.contactItem}>
                <Mail size={16} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.contactText}><EMAIL></Text>
              </View>
              <View style={styles.contactItem}>
                <Phone size={16} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.contactText}>+33 (0)148304863</Text>
              </View>
            </View>
          </LinearGradient>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
    color: '#111827',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    color: '#6B7280',
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 20,
    color: '#111827',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 20,
  },
  mainServiceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  serviceMainImage: {
    width: '100%',
    height: 160,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  serviceMainContent: {
    padding: 20,
  },
  serviceMainHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 12,
  },
  serviceMainTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    color: '#111827',
  },
  serviceMainDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  serviceFeatures: {
    gap: 8,
    marginBottom: 16,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#374151',
  },
  serviceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: '#F0FDF4',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  serviceButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    color: '#10B981',
  },
  processContainer: {
    gap: 16,
  },
  processStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
  },
  stepNumber: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepNumberText: {
    fontFamily: 'Inter-Bold',
    fontSize: 16,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    color: '#111827',
    marginBottom: 4,
  },
  stepDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  additionalServices: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  additionalServiceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    width: (width - 64) / 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  additionalServiceTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
    color: '#111827',
    marginTop: 12,
    marginBottom: 8,
    textAlign: 'center',
  },
  additionalServiceDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 16,
  },
 ctaSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  ctaCard: {
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.3,
    shadowRadius: 24,
    elevation: 12,
  },
  ctaTitle: {
    fontSize: 24,
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  ctaSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    opacity: 0.9,
    marginBottom: 32,
    lineHeight: 24,
  },
  ctaButtons: {
    width: '100%',
    gap: 16,
    marginBottom: 32,
  },
  certificationBodies: {
    paddingHorizontal: 20,
  },
  certificationCard: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  certificationLogo: {
    width: 200,
    height: 200,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  certificationInfo: {
    flex: 1,
  },
  certificationName: {
    fontSize: 16,
    color: '#111827',
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  certificationDescription: {
    fontSize: 13,
    color: '#6B7280',
    fontFamily: 'Inter-Regular',
    lineHeight: 18,
  },
  verifiedBadge: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0FDF4',
    justifyContent: 'center',
    alignItems: 'center',
  },
   ctaPrimaryButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    alignItems: 'center',
  },
  ctaPrimaryButtonText: {
    color: '#059669',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  ctaSecondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  ctaSecondaryButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginLeft: 8,
  },
   contactInfo: {
    alignItems: 'center',
    gap: 12,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginLeft: 8,
    opacity: 0.9,
  },
});